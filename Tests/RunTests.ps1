# PowerShell script to run UBA Stacker Controller tests
# Provides various test execution options with detailed output

param(
    [string]$TestFilter = "",
    [switch]$Coverage = $false,
    [switch]$Verbose = $false,
    [switch]$NoBuild = $false,
    [string]$Logger = "console;verbosity=normal"
)

Write-Host "=== UBA Stacker Controller Test Runner ===" -ForegroundColor Green
Write-Host ""

# Set the test project path
$TestProject = "UBAStackerController.Tests.csproj"
$TestDirectory = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to test directory
Push-Location $TestDirectory

try {
    # Build the test project first (unless --no-build is specified)
    if (-not $NoBuild) {
        Write-Host "Building test project..." -ForegroundColor Yellow
        dotnet build $TestProject --configuration Debug
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed. Exiting." -ForegroundColor Red
            exit 1
        }
        Write-Host "Build completed successfully." -ForegroundColor Green
        Write-Host ""
    }

    # Prepare test command
    $TestCommand = "dotnet test $TestProject"
    
    # Add test filter if specified
    if ($TestFilter) {
        $TestCommand += " --filter `"$TestFilter`""
        Write-Host "Running tests with filter: $TestFilter" -ForegroundColor Cyan
    } else {
        Write-Host "Running all tests..." -ForegroundColor Cyan
    }

    # Add coverage collection if requested
    if ($Coverage) {
        $TestCommand += " --collect:`"XPlat Code Coverage`""
        Write-Host "Code coverage collection enabled." -ForegroundColor Cyan
    }

    # Add verbosity if requested
    if ($Verbose) {
        $TestCommand += " --verbosity detailed"
    } else {
        $TestCommand += " --verbosity normal"
    }

    # Add logger configuration
    $TestCommand += " --logger `"$Logger`""

    # Add no-build flag if building was skipped
    if ($NoBuild) {
        $TestCommand += " --no-build"
    }

    Write-Host ""
    Write-Host "Executing: $TestCommand" -ForegroundColor Gray
    Write-Host ""

    # Execute the test command
    Invoke-Expression $TestCommand

    # Check test results
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "=== All tests passed! ===" -ForegroundColor Green
        
        if ($Coverage) {
            Write-Host ""
            Write-Host "Coverage reports generated in TestResults folder." -ForegroundColor Yellow
            Write-Host "To view coverage report, use a tool like ReportGenerator:" -ForegroundColor Yellow
            Write-Host "  dotnet tool install -g dotnet-reportgenerator-globaltool" -ForegroundColor Gray
            Write-Host "  reportgenerator -reports:TestResults/*/coverage.cobertura.xml -targetdir:TestResults/CoverageReport" -ForegroundColor Gray
        }
    } else {
        Write-Host ""
        Write-Host "=== Some tests failed! ===" -ForegroundColor Red
        Write-Host "Check the output above for details." -ForegroundColor Red
    }

} catch {
    Write-Host "Error occurred while running tests: $_" -ForegroundColor Red
    exit 1
} finally {
    # Return to original directory
    Pop-Location
}

Write-Host ""
Write-Host "=== Test execution completed ===" -ForegroundColor Green

# Examples of usage:
Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Yellow
Write-Host "  .\RunTests.ps1                                    # Run all tests" -ForegroundColor Gray
Write-Host "  .\RunTests.ps1 -TestFilter 'StackerServiceTests'  # Run specific test class" -ForegroundColor Gray
Write-Host "  .\RunTests.ps1 -Coverage                          # Run with code coverage" -ForegroundColor Gray
Write-Host "  .\RunTests.ps1 -Verbose                           # Run with detailed output" -ForegroundColor Gray
Write-Host "  .\RunTests.ps1 -NoBuild                           # Skip build step" -ForegroundColor Gray

# UBA Stacker Controller - Unit Test Documentation

## Overview
This document provides a comprehensive overview of the NUnit test projects created for testing the UBA Stacker Controller services. The test suite covers two main services: **StackerService** and **FanucRobotService**.

## Test Project Structure

```
Tests/
├── UBAStackerController.Tests.csproj    # Test project configuration
├── Services/
│   ├── StackerServiceTests.cs           # Stacker service unit tests
│   └── FanucRobotServiceTests.cs        # Fanuc robot service unit tests
└── TestSummary.md                       # This documentation file
```

## Testing Framework and Dependencies

- **NUnit 4.0.1** - Primary testing framework
- **Moq 4.20.69** - Mocking framework for dependencies
- **Microsoft.NET.Test.Sdk** - Test SDK for .NET
- **coverlet.collector** - Code coverage collection

---

## Stacker Service Tests

### Service Overview
The `StackerService` manages multiple stacker devices, handling initialization, state retrieval, and device management operations.

### Key Functionalities Tested

1. **Stacker Initialization**
   - Configuration binding and parsing
   - Serial port setup and connection
   - Error handling for failed initializations

2. **Stacker Retrieval**
   - Getting stackers by name
   - Handling non-existent stackers

3. **State Management**
   - Retrieving all stacker states
   - Handling empty stacker collections

### Test Cases

#### `InitializeStackers_WithValidConfiguration_ShouldInitializeStackersSuccessfully`
- **Purpose**: Validates stacker initialization from configuration
- **Arrange**: Mock configuration with valid stacker info
- **Act**: Call InitializeStackers()
- **Assert**: Verify logging and error handling (serial ports fail in test environment)

#### `GetStacker_WithExistingStackerName_ShouldReturnCorrectStacker`
- **Purpose**: Tests successful stacker retrieval
- **Arrange**: Add test stacker to service
- **Act**: Call GetStacker() with valid name
- **Assert**: Returns correct stacker instance

#### `GetStacker_WithNonExistentStackerName_ShouldReturnNull`
- **Purpose**: Tests handling of invalid stacker names
- **Arrange**: Empty stacker collection
- **Act**: Call GetStacker() with non-existent name
- **Assert**: Returns null

#### `GetStackersState_WithMultipleStackers_ShouldReturnAllStackerStates`
- **Purpose**: Tests state retrieval for multiple stackers
- **Arrange**: Add multiple test stackers
- **Act**: Call GetStackersState()
- **Assert**: Returns all stacker states

#### `GetStackersState_WithNoStackers_ShouldReturnEmptyList`
- **Purpose**: Tests state retrieval with no stackers
- **Arrange**: Empty stacker collection
- **Act**: Call GetStackersState()
- **Assert**: Returns empty list

#### `InitializeStackers_WithConfigurationBindingError_ShouldHandleGracefully`
- **Purpose**: Tests error handling for configuration issues
- **Arrange**: Mock configuration to throw exception
- **Act**: Call InitializeStackers()
- **Assert**: No exception thrown, graceful handling

#### `Stackers_Property_ShouldBeInitializedAndAccessible`
- **Purpose**: Tests property initialization
- **Arrange**: Service instance
- **Act**: Access Stackers property
- **Assert**: Property is initialized and accessible

---

## Fanuc Robot Service Tests

### Service Overview
The `FanucRobotService` handles communication with Fanuc robots via EEIP protocol, supporting numeric and string register operations.

### Key Functionalities Tested

1. **Numeric Register Operations**
   - Writing byte arrays to numeric registers
   - Handling duplicate value optimization
   - Connection management

2. **String Register Operations**
   - Writing strings with proper FANUC formatting
   - 88-byte string structure validation
   - String length validation (82-byte limit)

3. **Connection Management**
   - EEIP session registration/unregistration
   - Error handling and cleanup

4. **Configuration Management**
   - Enabled/disabled state handling
   - Register settings validation

### Test Cases

#### `WriteToNumericRegisterAsync_WithValidData_ShouldWriteSuccessfully`
- **Purpose**: Tests successful numeric register write
- **Arrange**: Mock EEIP client with valid response
- **Act**: Call WriteToNumericRegisterAsync()
- **Assert**: Verify session management and SetAttributeSingle call

#### `WriteToNumericRegisterAsync_WhenDisabled_ShouldSkipOperation`
- **Purpose**: Tests disabled integration handling
- **Arrange**: Set Enabled = false in settings
- **Act**: Call WriteToNumericRegisterAsync()
- **Assert**: No EEIP operations performed, debug log generated

#### `WriteToStringRegisterAsync_WithValidString_ShouldWriteSuccessfully`
- **Purpose**: Tests successful string register write
- **Arrange**: Mock EEIP client, valid string input
- **Act**: Call WriteToStringRegisterAsync()
- **Assert**: Verify 88-byte FANUC string format and write operation

#### `WriteToStringRegisterAsync_WithOversizedString_ShouldThrowArgumentException`
- **Purpose**: Tests string length validation
- **Arrange**: String exceeding 82-byte limit
- **Act**: Call WriteToStringRegisterAsync()
- **Assert**: ArgumentException thrown with appropriate message

#### `ReadRegisterAsync_WithValidRegister_ShouldReadSuccessfully`
- **Purpose**: Tests successful register read operation
- **Arrange**: Mock EEIP client with response data
- **Act**: Call ReadRegisterAsync()
- **Assert**: Verify session management and GetAttributeSingle call

#### `WriteToNumericRegisterAsync_WithEEIPException_ShouldHandleGracefully`
- **Purpose**: Tests exception handling in EEIP operations
- **Arrange**: Mock EEIP client to throw exception
- **Act**: Call WriteToNumericRegisterAsync()
- **Assert**: Exception handled gracefully, error logged, session cleaned up

#### `WriteToNumericRegisterAsync_WithDuplicateValue_ShouldSkipSecondWrite`
- **Purpose**: Tests duplicate value optimization
- **Arrange**: Same value written twice
- **Act**: Call WriteToNumericRegisterAsync() twice
- **Assert**: SetAttributeSingle called only once, trace log for skip

---

## Test Patterns and Best Practices

### Arrange-Act-Assert Pattern
All tests follow the AAA pattern:
- **Arrange**: Set up test data, mocks, and preconditions
- **Act**: Execute the method under test
- **Assert**: Verify expected outcomes and behaviors

### Mocking Strategy
- **Dependencies**: All external dependencies are mocked using Moq
- **Isolation**: Each test is isolated and doesn't depend on external systems
- **Verification**: Mock interactions are verified to ensure correct behavior

### Setup and Teardown
- **SetUp**: Initializes mocks and service instances before each test
- **TearDown**: Cleans up resources after each test
- **Consistency**: Ensures clean state for each test execution

### Error Handling Tests
- Tests verify graceful handling of various error conditions
- Exception scenarios are tested to ensure robustness
- Logging behavior is verified for troubleshooting support

---

## Running the Tests

### Prerequisites
- .NET 9 SDK
- NUnit Test Adapter
- Referenced libraries (ID003ProtocolManager.dll, EEIP.dll)

### Execution Commands
```bash
# Run all tests
dotnet test Tests/UBAStackerController.Tests.csproj

# Run with coverage
dotnet test Tests/UBAStackerController.Tests.csproj --collect:"XPlat Code Coverage"

# Run specific test class
dotnet test Tests/UBAStackerController.Tests.csproj --filter "ClassName=StackerServiceTests"

# Run in verbose mode
dotnet test Tests/UBAStackerController.Tests.csproj --verbosity normal
```

### Expected Results
- **StackerService Tests**: 7 test cases
- **FanucRobotService Tests**: 8 test cases
- **Total**: 15 comprehensive test cases covering core functionality

---

## Coverage Goals

The test suite aims to achieve:
- **Method Coverage**: 100% of public methods tested
- **Branch Coverage**: All major code paths covered
- **Error Scenarios**: Exception handling and edge cases tested
- **Integration Points**: Mock verification for all dependencies

---

## Future Enhancements

1. **Integration Tests**: Add tests that verify service interactions
2. **Performance Tests**: Add tests for high-load scenarios
3. **Configuration Tests**: Expand configuration validation testing
4. **End-to-End Tests**: Add tests that simulate real device interactions

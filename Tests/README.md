# UBA Stacker Controller - Test Suite

This directory contains comprehensive unit tests for the UBA Stacker Controller application, focusing on testing the core services: **StackerService** and **FanucRobotService**.

## 🏗️ Project Structure

```
Tests/
├── UBAStackerController.Tests.csproj    # Test project configuration
├── Services/
│   ├── StackerServiceTests.cs           # Stacker service unit tests (7 test cases)
│   └── FanucRobotServiceTests.cs        # Fanuc robot service unit tests (8 test cases)
├── Helpers/
│   ├── TestDataBuilder.cs               # Fluent builders for test data
│   └── MockConfigurationHelper.cs       # Configuration mocking utilities
├── RunTests.ps1                         # PowerShell test runner
├── run-tests.sh                         # Bash test runner (Linux/macOS)
├── TestSummary.md                       # Detailed test documentation
└── README.md                            # This file
```

## 🚀 Quick Start

### Prerequisites
- .NET 9 SDK
- NUnit Test Adapter
- Referenced libraries (ID003ProtocolManager.dll, EEIP.dll)

### Running Tests

#### Option 1: Using Test Runners (Recommended)

**Windows (PowerShell):**
```powershell
# Run all tests
.\RunTests.ps1

# Run with code coverage
.\RunTests.ps1 -Coverage

# Run specific test class
.\RunTests.ps1 -TestFilter "StackerServiceTests"

# Run with verbose output
.\RunTests.ps1 -Verbose
```

**Linux/macOS (Bash):**
```bash
# Run all tests
./run-tests.sh

# Run with code coverage
./run-tests.sh -c

# Run specific test class
./run-tests.sh -f "StackerServiceTests"

# Run with verbose output
./run-tests.sh -v
```

#### Option 2: Using .NET CLI Directly

```bash
# Run all tests
dotnet test UBAStackerController.Tests.csproj

# Run with coverage
dotnet test UBAStackerController.Tests.csproj --collect:"XPlat Code Coverage"

# Run specific test class
dotnet test UBAStackerController.Tests.csproj --filter "ClassName=StackerServiceTests"

# Run in verbose mode
dotnet test UBAStackerController.Tests.csproj --verbosity detailed
```

## 📊 Test Coverage

### Stacker Service Tests (7 test cases)
- ✅ **Initialization**: Configuration binding, serial port setup, error handling
- ✅ **Retrieval**: Getting stackers by name, handling non-existent stackers
- ✅ **State Management**: Retrieving all stacker states, empty collections
- ✅ **Error Handling**: Configuration binding errors, graceful degradation
- ✅ **Property Access**: Dictionary initialization and accessibility

### Fanuc Robot Service Tests (8 test cases)
- ✅ **Numeric Registers**: Writing byte arrays, duplicate value optimization
- ✅ **String Registers**: FANUC 88-byte format, length validation
- ✅ **Connection Management**: EEIP session handling, cleanup
- ✅ **Configuration**: Enabled/disabled state, settings validation
- ✅ **Error Handling**: EEIP exceptions, graceful recovery
- ✅ **Performance**: Duplicate write detection, trace logging

## 🛠️ Test Utilities

### TestDataBuilder
Provides fluent builders for creating test data:

```csharp
// Create stacker info
var stackerInfo = TestDataBuilder.CreateStackerInfo()
    .WithName("TestStacker")
    .WithPort("COM1")
    .WithBaudRate(9600)
    .Build();

// Create stacker state
var stackerState = TestDataBuilder.CreateStackerState()
    .WithName("TestStacker")
    .WithStatus(StackerStatus.Idling)
    .WithIsActive(true)
    .Build();

// Create Fanuc settings
var fanucSettings = TestDataBuilder.CreateFanucRobotSettings()
    .WithIpAddress("*************")
    .WithEnabled(true)
    .WithNumericRegister("0x6B", 1, 31)
    .Build();
```

### MockConfigurationHelper
Simplifies configuration mocking:

```csharp
// Mock configuration with stackers
var mockConfig = MockConfigurationHelper
    .CreateMockConfigurationWithStackers(stackerInfos);

// Mock configuration with Fanuc settings
var mockConfig = MockConfigurationHelper
    .CreateMockConfigurationWithFanuc(fanucSettings);

// Mock configuration with binding errors
var mockConfig = MockConfigurationHelper
    .CreateMockConfigurationWithBindingError(new InvalidOperationException());
```

## 🎯 Testing Patterns

### Arrange-Act-Assert (AAA)
All tests follow the AAA pattern for clarity and consistency:

```csharp
[Test]
public void TestMethod_Scenario_ExpectedResult()
{
    // Arrange - Set up test data and mocks
    var mockService = new Mock<IService>();
    var testData = CreateTestData();
    
    // Act - Execute the method under test
    var result = serviceUnderTest.Method(testData);
    
    // Assert - Verify expected outcomes
    Assert.That(result, Is.Not.Null);
    mockService.Verify(x => x.Method(), Times.Once);
}
```

### Mocking Strategy
- **Complete Isolation**: All external dependencies are mocked
- **Behavior Verification**: Mock interactions are verified
- **State Testing**: Object state changes are validated
- **Exception Testing**: Error scenarios are thoroughly tested

## 📈 Code Coverage Goals

- **Method Coverage**: 100% of public methods
- **Branch Coverage**: All major code paths
- **Error Scenarios**: Exception handling and edge cases
- **Integration Points**: All dependency interactions

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**
   - Ensure .NET 9 SDK is installed
   - Verify all NuGet packages are restored
   - Check that referenced DLLs are present in `../lib/`

2. **Test Failures**
   - Serial port tests may fail in containerized environments
   - Mock setup issues - verify mock configurations
   - Timing issues - check async test patterns

3. **Coverage Issues**
   - Install ReportGenerator: `dotnet tool install -g dotnet-reportgenerator-globaltool`
   - Generate reports: `reportgenerator -reports:TestResults/*/coverage.cobertura.xml -targetdir:TestResults/CoverageReport`

### Debug Mode
Run tests in debug mode for detailed troubleshooting:

```bash
dotnet test --configuration Debug --verbosity diagnostic
```

## 📚 Additional Resources

- **Detailed Documentation**: See `TestSummary.md` for comprehensive test descriptions
- **NUnit Documentation**: [https://docs.nunit.org/](https://docs.nunit.org/)
- **Moq Documentation**: [https://github.com/moq/moq4](https://github.com/moq/moq4)
- **.NET Testing**: [https://docs.microsoft.com/en-us/dotnet/core/testing/](https://docs.microsoft.com/en-us/dotnet/core/testing/)

## 🤝 Contributing

When adding new tests:

1. Follow the existing AAA pattern
2. Use descriptive test method names: `Method_Scenario_ExpectedResult`
3. Include XML documentation for test classes
4. Add appropriate assertions with meaningful messages
5. Mock all external dependencies
6. Update this README if adding new test categories

## 📝 Test Execution Results

Expected output when all tests pass:
```
Test run for UBAStackerController.Tests.dll (.NET 9.0)
Microsoft (R) Test Execution Command Line Tool Version 17.8.0

Starting test execution, please wait...
A total of 15 test files matched the specified pattern.

Passed!  - Failed:     0, Passed:    15, Skipped:     0, Total:    15
```

---

**Total Test Cases**: 15 (7 StackerService + 8 FanucRobotService)  
**Framework**: NUnit 4.0.1  
**Mocking**: Moq 4.20.69  
**Target**: .NET 9.0

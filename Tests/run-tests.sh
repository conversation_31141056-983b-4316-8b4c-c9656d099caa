#!/bin/bash

# Bash script to run UBA Stacker Controller tests
# Provides various test execution options with detailed output

# Default values
TEST_FILTER=""
COVERAGE=false
VERBOSE=false
NO_BUILD=false
LOGGER="console;verbosity=normal"

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -f, --filter FILTER    Run tests matching the specified filter"
    echo "  -c, --coverage         Enable code coverage collection"
    echo "  -v, --verbose          Enable verbose output"
    echo "  -n, --no-build         Skip build step"
    echo "  -l, --logger LOGGER    Specify logger configuration"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests"
    echo "  $0 -f 'StackerServiceTests'          # Run specific test class"
    echo "  $0 -c                                 # Run with code coverage"
    echo "  $0 -v                                 # Run with detailed output"
    echo "  $0 -n                                 # Skip build step"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--filter)
            TEST_FILTER="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -n|--no-build)
            NO_BUILD=true
            shift
            ;;
        -l|--logger)
            LOGGER="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== UBA Stacker Controller Test Runner ===${NC}"
echo ""

# Set the test project path
TEST_PROJECT="UBAStackerController.Tests.csproj"
TEST_DIRECTORY="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to test directory
cd "$TEST_DIRECTORY" || exit 1

# Build the test project first (unless --no-build is specified)
if [ "$NO_BUILD" = false ]; then
    echo -e "${YELLOW}Building test project...${NC}"
    dotnet build "$TEST_PROJECT" --configuration Debug
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Build failed. Exiting.${NC}"
        exit 1
    fi
    echo -e "${GREEN}Build completed successfully.${NC}"
    echo ""
fi

# Prepare test command
TEST_COMMAND="dotnet test $TEST_PROJECT"

# Add test filter if specified
if [ -n "$TEST_FILTER" ]; then
    TEST_COMMAND="$TEST_COMMAND --filter \"$TEST_FILTER\""
    echo -e "${CYAN}Running tests with filter: $TEST_FILTER${NC}"
else
    echo -e "${CYAN}Running all tests...${NC}"
fi

# Add coverage collection if requested
if [ "$COVERAGE" = true ]; then
    TEST_COMMAND="$TEST_COMMAND --collect:\"XPlat Code Coverage\""
    echo -e "${CYAN}Code coverage collection enabled.${NC}"
fi

# Add verbosity if requested
if [ "$VERBOSE" = true ]; then
    TEST_COMMAND="$TEST_COMMAND --verbosity detailed"
else
    TEST_COMMAND="$TEST_COMMAND --verbosity normal"
fi

# Add logger configuration
TEST_COMMAND="$TEST_COMMAND --logger \"$LOGGER\""

# Add no-build flag if building was skipped
if [ "$NO_BUILD" = true ]; then
    TEST_COMMAND="$TEST_COMMAND --no-build"
fi

echo ""
echo -e "${GRAY}Executing: $TEST_COMMAND${NC}"
echo ""

# Execute the test command
eval "$TEST_COMMAND"
TEST_EXIT_CODE=$?

# Check test results
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo ""
    echo -e "${GREEN}=== All tests passed! ===${NC}"
    
    if [ "$COVERAGE" = true ]; then
        echo ""
        echo -e "${YELLOW}Coverage reports generated in TestResults folder.${NC}"
        echo -e "${YELLOW}To view coverage report, use a tool like ReportGenerator:${NC}"
        echo -e "${GRAY}  dotnet tool install -g dotnet-reportgenerator-globaltool${NC}"
        echo -e "${GRAY}  reportgenerator -reports:TestResults/*/coverage.cobertura.xml -targetdir:TestResults/CoverageReport${NC}"
    fi
else
    echo ""
    echo -e "${RED}=== Some tests failed! ===${NC}"
    echo -e "${RED}Check the output above for details.${NC}"
fi

echo ""
echo -e "${GREEN}=== Test execution completed ===${NC}"

# Examples of usage
echo ""
echo -e "${YELLOW}Usage examples:${NC}"
echo -e "${GRAY}  ./run-tests.sh                                    # Run all tests${NC}"
echo -e "${GRAY}  ./run-tests.sh -f 'StackerServiceTests'          # Run specific test class${NC}"
echo -e "${GRAY}  ./run-tests.sh -c                                 # Run with code coverage${NC}"
echo -e "${GRAY}  ./run-tests.sh -v                                 # Run with detailed output${NC}"
echo -e "${GRAY}  ./run-tests.sh -n                                 # Skip build step${NC}"

exit $TEST_EXIT_CODE

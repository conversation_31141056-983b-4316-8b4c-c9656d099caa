using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using UBAStackerController.Services.Implementations;
using UBAStackerController.Services.Interfaces;
using Sres.Net.EEIP;
using System.Text;

namespace UBAStackerController.Tests.Services
{
    /// <summary>
    /// Unit tests for the FanucRobotService class.
    /// Tests the core functionality of Fanuc robot communication including
    /// register read/write operations and connection management.
    /// </summary>
    [TestFixture]
    public class FanucRobotServiceTests
    {
        #region Private Fields

        private Mock<EEIPClient> _mockEEIPClient;
        private Mock<ILogger<FanucRobotService>> _mockLogger;
        private Mock<IOptions<FanucRobotSettings>> _mockOptions;
        private FanucRobotSettings _fanucSettings;
        private FanucRobotService _fanucRobotService;

        #endregion

        #region Setup and Teardown

        /// <summary>
        /// Sets up the test environment before each test.
        /// Initializes all mock objects and the service under test.
        /// </summary>
        [SetUp]
        public void Setup()
        {
            // Arrange - Initialize all mocks
            _mockEEIPClient = new Mock<EEIPClient>();
            _mockLogger = new Mock<ILogger<FanucRobotService>>();
            _mockOptions = new Mock<IOptions<FanucRobotSettings>>();

            // Setup Fanuc settings
            _fanucSettings = new FanucRobotSettings
            {
                IpAddress = "*************",
                Enabled = true,
                NumericRegister = new NumericRegisterSettings
                {
                    ClassId = "0x6B",
                    InstanceId = 1,
                    Attribute = 31,
                    Service = "SetAttributeSingle"
                },
                StringRegister = new StringRegisterSettings
                {
                    ClassId = "0x6D",
                    InstanceId = 1,
                    Attribute = 11,
                    Service = "SetAttributeSingle"
                }
            };

            _mockOptions.Setup(x => x.Value).Returns(_fanucSettings);

            // Create service instance
            _fanucRobotService = new FanucRobotService(
                _mockEEIPClient.Object,
                _mockOptions.Object,
                _mockLogger.Object);
        }

        /// <summary>
        /// Cleans up resources after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            // Clean up any resources if needed
        }

        #endregion

        #region Test Cases

        /// <summary>
        /// Tests that WriteToNumericRegisterAsync successfully writes to a numeric register.
        /// Expected: EEIP client SetAttributeSingle is called with correct parameters.
        /// </summary>
        [Test]
        public async Task WriteToNumericRegisterAsync_WithValidData_ShouldWriteSuccessfully()
        {
            // Arrange
            int testRegister = 31;
            byte[] testValue = BitConverter.GetBytes(12345u);
            byte[] expectedResponse = new byte[] { 0x00, 0x00 };

            _mockEEIPClient.Setup(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()))
                .Returns(expectedResponse);

            // Act
            await _fanucRobotService.WriteToNumericRegisterAsync(testRegister, testValue);

            // Assert
            _mockEEIPClient.Verify(x => x.RegisterSession(_fanucSettings.IpAddress), Times.Once,
                "Should register EEIP session");
            _mockEEIPClient.Verify(x => x.SetAttributeSingle(
                0x6B,
                1,
                testRegister,
                testValue), Times.Once,
                "Should call SetAttributeSingle with correct parameters");
            _mockEEIPClient.Verify(x => x.UnRegisterSession(), Times.Once,
                "Should unregister EEIP session");
        }

        /// <summary>
        /// Tests that WriteToNumericRegisterAsync skips operation when Fanuc integration is disabled.
        /// Expected: No EEIP operations are performed when Enabled is false.
        /// </summary>
        [Test]
        public async Task WriteToNumericRegisterAsync_WhenDisabled_ShouldSkipOperation()
        {
            // Arrange
            _fanucSettings.Enabled = false;
            int testRegister = 31;
            byte[] testValue = BitConverter.GetBytes(12345u);

            // Act
            await _fanucRobotService.WriteToNumericRegisterAsync(testRegister, testValue);

            // Assert
            _mockEEIPClient.Verify(x => x.RegisterSession(It.IsAny<string>()), Times.Never,
                "Should not register session when disabled");
            _mockEEIPClient.Verify(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()), Times.Never,
                "Should not call SetAttributeSingle when disabled");

            // Verify debug log was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Fanuc integration disabled")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        /// <summary>
        /// Tests that WriteToStringRegisterAsync successfully writes to a string register.
        /// Expected: String is properly formatted and written to the register.
        /// </summary>
        [Test]
        public async Task WriteToStringRegisterAsync_WithValidString_ShouldWriteSuccessfully()
        {
            // Arrange
            int testRegister = 11;
            string testValue = "TEST_STRING";
            byte[] expectedResponse = new byte[] { 0x00, 0x00 };

            _mockEEIPClient.Setup(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()))
                .Returns(expectedResponse);

            // Act
            await _fanucRobotService.WriteToStringRegisterAsync(testRegister, testValue);

            // Assert
            _mockEEIPClient.Verify(x => x.RegisterSession(_fanucSettings.IpAddress), Times.Once,
                "Should register EEIP session");
            
            _mockEEIPClient.Verify(x => x.SetAttributeSingle(
                0x6D,
                1,
                testRegister,
                It.Is<byte[]>(data => 
                    data.Length == 88 && 
                    BitConverter.ToUInt32(data, 0) == (uint)testValue.Length)),
                Times.Once,
                "Should call SetAttributeSingle with properly formatted 88-byte string data");
            
            _mockEEIPClient.Verify(x => x.UnRegisterSession(), Times.Once,
                "Should unregister EEIP session");
        }

        /// <summary>
        /// Tests that WriteToStringRegisterAsync throws exception for strings exceeding 82 bytes.
        /// Expected: ArgumentException is thrown for oversized strings.
        /// </summary>
        [Test]
        public void WriteToStringRegisterAsync_WithOversizedString_ShouldThrowArgumentException()
        {
            // Arrange
            int testRegister = 11;
            string oversizedString = new string('A', 83); // 83 characters > 82 byte limit

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(async () =>
                await _fanucRobotService.WriteToStringRegisterAsync(testRegister, oversizedString));

            Assert.That(ex.Message, Does.Contain("String value exceeds maximum length of 82 bytes"),
                "Exception message should indicate string length limit exceeded");
        }

        /// <summary>
        /// Tests that ReadRegisterAsync successfully reads from a register.
        /// Expected: EEIP client GetAttributeSingle is called with correct parameters.
        /// </summary>
        [Test]
        public async Task ReadRegisterAsync_WithValidRegister_ShouldReadSuccessfully()
        {
            // Arrange
            int testRegister = 31;
            byte[] expectedResponse = BitConverter.GetBytes(54321u);

            _mockEEIPClient.Setup(x => x.GetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
                .Returns(expectedResponse);

            // Act
            await _fanucRobotService.ReadRegisterAsync(testRegister);

            // Assert
            _mockEEIPClient.Verify(x => x.RegisterSession(_fanucSettings.IpAddress), Times.Once,
                "Should register EEIP session");
            _mockEEIPClient.Verify(x => x.GetAttributeSingle(
                0x6B,
                1,
                _fanucSettings.NumericRegister.Attribute), Times.Once,
                "Should call GetAttributeSingle with correct parameters");
            _mockEEIPClient.Verify(x => x.UnRegisterSession(), Times.Once,
                "Should unregister EEIP session");
        }

        /// <summary>
        /// Tests that operations handle EEIP client exceptions gracefully.
        /// Expected: Exceptions are logged and session is properly cleaned up.
        /// </summary>
        [Test]
        public async Task WriteToNumericRegisterAsync_WithEEIPException_ShouldHandleGracefully()
        {
            // Arrange
            int testRegister = 31;
            byte[] testValue = BitConverter.GetBytes(12345u);
            var expectedException = new InvalidOperationException("EEIP communication failed");

            _mockEEIPClient.Setup(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()))
                .Throws(expectedException);

            // Act & Assert
            Assert.DoesNotThrowAsync(async () =>
                await _fanucRobotService.WriteToNumericRegisterAsync(testRegister, testValue),
                "Should handle EEIP exceptions gracefully");

            // Verify error was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error writing Fanuc register")),
                    expectedException,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            // Verify session cleanup occurred
            _mockEEIPClient.Verify(x => x.UnRegisterSession(), Times.Once,
                "Should unregister session even when exception occurs");
        }

        /// <summary>
        /// Tests that duplicate writes are skipped to optimize performance.
        /// Expected: Second write with same value is skipped.
        /// </summary>
        [Test]
        public async Task WriteToNumericRegisterAsync_WithDuplicateValue_ShouldSkipSecondWrite()
        {
            // Arrange
            int testRegister = 31;
            byte[] testValue = BitConverter.GetBytes(12345u);
            byte[] expectedResponse = new byte[] { 0x00, 0x00 };

            _mockEEIPClient.Setup(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()))
                .Returns(expectedResponse);

            // Act - Write same value twice
            await _fanucRobotService.WriteToNumericRegisterAsync(testRegister, testValue);
            await _fanucRobotService.WriteToNumericRegisterAsync(testRegister, testValue);

            // Assert - SetAttributeSingle should only be called once
            _mockEEIPClient.Verify(x => x.SetAttributeSingle(
                It.IsAny<ushort>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<byte[]>()), Times.Once,
                "Should only call SetAttributeSingle once for duplicate values");

            // Verify trace log for skipped write
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Trace,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Skipping write to Fanuc register")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion
    }
}

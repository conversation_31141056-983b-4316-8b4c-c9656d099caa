using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using UBAStackerController.Services.Implementations;
using UBAStackerController.Services.Interfaces;
using UBAStackerController.Models;
using ID003ProtocolManager;
using System.IO.Ports;

namespace UBAStackerController.Tests.Services
{
    /// <summary>
    /// Unit tests for the StackerService class.
    /// Tests the core functionality of stacker management including initialization,
    /// retrieval, and state management operations.
    /// </summary>
    [TestFixture]
    public class StackerServiceTests
    {
        #region Private Fields

        private Mock<ILogger<StackerService>> _mockLogger;
        private Mock<ILoggerFactory> _mockLoggerFactory;
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<ID003CommandCreater> _mockCommandCreator;
        private Mock<IPollingStateService> _mockPollingStateService;
        private Mock<IConfigurationSection> _mockConfigSection;
        private Mock<ILogger<Stacker>> _mockStackerLogger;
        private StackerService _stackerService;

        #endregion

        #region Setup and Teardown

        /// <summary>
        /// Sets up the test environment before each test.
        /// Initializes all mock objects and the service under test.
        /// </summary>
        [SetUp]
        public void Setup()
        {
            // Arrange - Initialize all mocks
            _mockLogger = new Mock<ILogger<StackerService>>();
            _mockLoggerFactory = new Mock<ILoggerFactory>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockCommandCreator = new Mock<ID003CommandCreater>();
            _mockPollingStateService = new Mock<IPollingStateService>();
            _mockConfigSection = new Mock<IConfigurationSection>();
            _mockStackerLogger = new Mock<ILogger<Stacker>>();

            // Setup logger factory to return stacker logger
            _mockLoggerFactory.Setup(x => x.CreateLogger<Stacker>())
                             .Returns(_mockStackerLogger.Object);

            // Setup configuration section
            _mockConfiguration.Setup(x => x.GetSection("Stackers"))
                             .Returns(_mockConfigSection.Object);

            // Create service instance
            _stackerService = new StackerService(
                _mockConfiguration.Object,
                _mockLogger.Object,
                _mockLoggerFactory.Object,
                _mockCommandCreator.Object,
                _mockPollingStateService.Object);
        }

        /// <summary>
        /// Cleans up resources after each test.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            // Clean up any resources if needed
            // Note: StackerService doesn't implement IDisposable in the current implementation
        }

        #endregion

        #region Test Cases

        /// <summary>
        /// Tests that InitializeStackers successfully initializes stackers from configuration.
        /// Expected: Stackers are added to the dictionary and initialization is logged.
        /// </summary>
        [Test]
        public async Task InitializeStackers_WithValidConfiguration_ShouldInitializeStackersSuccessfully()
        {
            // Arrange
            var stackerInfos = new List<StackerInfo>
            {
                new StackerInfo { Name = "TestStacker1", Port = "COM1", BaudRate = 9600 },
                new StackerInfo { Name = "TestStacker2", Port = "COM2", BaudRate = 9600 }
            };

            _mockConfigSection.Setup(x => x.Bind(It.IsAny<List<StackerInfo>>()))
                             .Callback<List<StackerInfo>>(list =>
                             {
                                 list.AddRange(stackerInfos);
                             });

            // Act
            await _stackerService.InitializeStackers();

            // Assert
            Assert.That(_stackerService.Stackers.Count, Is.EqualTo(0), 
                "Stackers count should be 0 due to serial port initialization failure in test environment");
            
            // Verify logging occurred
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Failed to initialize")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeast(2));
        }

        /// <summary>
        /// Tests that GetStacker returns the correct stacker when it exists.
        /// Expected: Returns the stacker with matching name.
        /// </summary>
        [Test]
        public void GetStacker_WithExistingStackerName_ShouldReturnCorrectStacker()
        {
            // Arrange
            var mockPort = new Mock<SerialPort>();
            var testStacker = new Stacker("TestStacker", mockPort.Object, _mockCommandCreator.Object, _mockStackerLogger.Object);
            _stackerService.Stackers.Add("TestStacker", testStacker);

            // Act
            var result = _stackerService.GetStacker("TestStacker");

            // Assert
            Assert.That(result, Is.Not.Null, "Should return a stacker instance");
            Assert.That(result.Name, Is.EqualTo("TestStacker"), "Should return stacker with correct name");
        }

        /// <summary>
        /// Tests that GetStacker returns null when stacker doesn't exist.
        /// Expected: Returns null for non-existent stacker name.
        /// </summary>
        [Test]
        public void GetStacker_WithNonExistentStackerName_ShouldReturnNull()
        {
            // Arrange
            // No stackers added to the service

            // Act
            var result = _stackerService.GetStacker("NonExistentStacker");

            // Assert
            Assert.That(result, Is.Null, "Should return null for non-existent stacker");
        }

        /// <summary>
        /// Tests that GetStackersState returns all stacker states correctly.
        /// Expected: Returns list containing all stacker states.
        /// </summary>
        [Test]
        public void GetStackersState_WithMultipleStackers_ShouldReturnAllStackerStates()
        {
            // Arrange
            var mockPort1 = new Mock<SerialPort>();
            var mockPort2 = new Mock<SerialPort>();
            
            var stacker1 = new Stacker("Stacker1", mockPort1.Object, _mockCommandCreator.Object, _mockStackerLogger.Object);
            var stacker2 = new Stacker("Stacker2", mockPort2.Object, _mockCommandCreator.Object, _mockStackerLogger.Object);
            
            _stackerService.Stackers.Add("Stacker1", stacker1);
            _stackerService.Stackers.Add("Stacker2", stacker2);

            // Act
            var result = _stackerService.GetStackersState();

            // Assert
            Assert.That(result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Count, Is.EqualTo(2), "Should return states for all stackers");
            Assert.That(result.Any(s => s.Name == "Stacker1"), Is.True, "Should contain Stacker1 state");
            Assert.That(result.Any(s => s.Name == "Stacker2"), Is.True, "Should contain Stacker2 state");
        }

        /// <summary>
        /// Tests that GetStackersState returns empty list when no stackers exist.
        /// Expected: Returns empty list.
        /// </summary>
        [Test]
        public void GetStackersState_WithNoStackers_ShouldReturnEmptyList()
        {
            // Arrange
            // No stackers added

            // Act
            var result = _stackerService.GetStackersState();

            // Assert
            Assert.That(result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Count, Is.EqualTo(0), "Should return empty list when no stackers exist");
        }

        /// <summary>
        /// Tests that InitializeStackers handles configuration binding errors gracefully.
        /// Expected: Service continues to function even with configuration errors.
        /// </summary>
        [Test]
        public async Task InitializeStackers_WithConfigurationBindingError_ShouldHandleGracefully()
        {
            // Arrange
            _mockConfigSection.Setup(x => x.Bind(It.IsAny<List<StackerInfo>>()))
                             .Throws(new InvalidOperationException("Configuration binding failed"));

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => await _stackerService.InitializeStackers(),
                "Should handle configuration binding errors gracefully");

            Assert.That(_stackerService.Stackers.Count, Is.EqualTo(0),
                "No stackers should be initialized when configuration binding fails");
        }

        /// <summary>
        /// Tests that the Stackers property is properly initialized and accessible.
        /// Expected: Stackers dictionary is not null and initially empty.
        /// </summary>
        [Test]
        public void Stackers_Property_ShouldBeInitializedAndAccessible()
        {
            // Act & Assert
            Assert.That(_stackerService.Stackers, Is.Not.Null, "Stackers dictionary should be initialized");
            Assert.That(_stackerService.Stackers, Is.InstanceOf<Dictionary<string, Stacker>>(),
                "Stackers should be a Dictionary<string, Stacker>");
            Assert.That(_stackerService.Stackers.Count, Is.EqualTo(0),
                "Stackers dictionary should be initially empty");
        }

        #endregion
    }
}

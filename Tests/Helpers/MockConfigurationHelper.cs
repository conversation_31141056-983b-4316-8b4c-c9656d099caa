using Microsoft.Extensions.Configuration;
using Moq;
using UBAStackerController.Models;

namespace UBAStackerController.Tests.Helpers
{
    /// <summary>
    /// Helper class for creating mock configuration objects for testing.
    /// Provides convenient methods for setting up configuration scenarios.
    /// </summary>
    public static class MockConfigurationHelper
    {
        /// <summary>
        /// Creates a mock IConfiguration with stacker configuration.
        /// </summary>
        /// <param name="stackerInfos">List of stacker info objects to configure</param>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateMockConfigurationWithStackers(List<StackerInfo> stackerInfos)
        {
            var mockConfiguration = new Mock<IConfiguration>();
            var mockStackersSection = new Mock<IConfigurationSection>();

            // Setup the Stackers section
            mockConfiguration.Setup(x => x.GetSection("Stackers"))
                           .Returns(mockStackersSection.Object);

            // Setup the binding behavior
            mockStackersSection.Setup(x => x.Bind(It.IsAny<List<StackerInfo>>()))
                              .Callback<List<StackerInfo>>(list =>
                              {
                                  list.Clear();
                                  list.AddRange(stackerInfos);
                              });

            return mockConfiguration;
        }

        /// <summary>
        /// Creates a mock IConfiguration with Fanuc robot settings.
        /// </summary>
        /// <param name="fanucSettings">Fanuc robot settings to configure</param>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateMockConfigurationWithFanuc(FanucRobotSettings fanucSettings)
        {
            var mockConfiguration = new Mock<IConfiguration>();
            var mockFanucSection = new Mock<IConfigurationSection>();

            // Setup the FanucRobot section
            mockConfiguration.Setup(x => x.GetSection("FanucRobot"))
                           .Returns(mockFanucSection.Object);

            // Setup individual configuration values
            mockFanucSection.Setup(x => x["IpAddress"]).Returns(fanucSettings.IpAddress);
            mockFanucSection.Setup(x => x["Enabled"]).Returns(fanucSettings.Enabled.ToString());

            // Setup numeric register section
            var mockNumericSection = new Mock<IConfigurationSection>();
            mockFanucSection.Setup(x => x.GetSection("NumericRegister"))
                          .Returns(mockNumericSection.Object);
            
            mockNumericSection.Setup(x => x["ClassId"]).Returns(fanucSettings.NumericRegister.ClassId);
            mockNumericSection.Setup(x => x["InstanceId"]).Returns(fanucSettings.NumericRegister.InstanceId.ToString());
            mockNumericSection.Setup(x => x["Attribute"]).Returns(fanucSettings.NumericRegister.Attribute.ToString());

            // Setup string register section
            var mockStringSection = new Mock<IConfigurationSection>();
            mockFanucSection.Setup(x => x.GetSection("StringRegister"))
                          .Returns(mockStringSection.Object);
            
            mockStringSection.Setup(x => x["ClassId"]).Returns(fanucSettings.StringRegister.ClassId);
            mockStringSection.Setup(x => x["InstanceId"]).Returns(fanucSettings.StringRegister.InstanceId.ToString());
            mockStringSection.Setup(x => x["Attribute"]).Returns(fanucSettings.StringRegister.Attribute.ToString());

            return mockConfiguration;
        }

        /// <summary>
        /// Creates a mock IConfiguration that throws an exception during binding.
        /// Useful for testing error handling scenarios.
        /// </summary>
        /// <param name="exceptionToThrow">The exception to throw during binding</param>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateMockConfigurationWithBindingError(Exception exceptionToThrow)
        {
            var mockConfiguration = new Mock<IConfiguration>();
            var mockStackersSection = new Mock<IConfigurationSection>();

            mockConfiguration.Setup(x => x.GetSection("Stackers"))
                           .Returns(mockStackersSection.Object);

            mockStackersSection.Setup(x => x.Bind(It.IsAny<List<StackerInfo>>()))
                              .Throws(exceptionToThrow);

            return mockConfiguration;
        }

        /// <summary>
        /// Creates a mock IConfiguration with empty stacker configuration.
        /// </summary>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateMockConfigurationWithEmptyStackers()
        {
            return CreateMockConfigurationWithStackers(new List<StackerInfo>());
        }

        /// <summary>
        /// Creates a mock IConfiguration with serial port settings.
        /// </summary>
        /// <param name="portName">Serial port name</param>
        /// <param name="baudRate">Baud rate</param>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateMockConfigurationWithSerialPort(string portName, int baudRate)
        {
            var mockConfiguration = new Mock<IConfiguration>();
            var mockSerialPortSection = new Mock<IConfigurationSection>();

            mockConfiguration.Setup(x => x.GetSection("SerialPort"))
                           .Returns(mockSerialPortSection.Object);

            mockConfiguration.Setup(x => x["SerialPort:Name"]).Returns(portName);
            mockConfiguration.Setup(x => x["SerialPort:BaudRate"]).Returns(baudRate.ToString());

            return mockConfiguration;
        }

        /// <summary>
        /// Creates a comprehensive mock IConfiguration with all sections.
        /// </summary>
        /// <param name="stackerInfos">Stacker configuration</param>
        /// <param name="fanucSettings">Fanuc robot settings</param>
        /// <param name="serialPortName">Serial port name</param>
        /// <param name="serialBaudRate">Serial baud rate</param>
        /// <returns>Mock IConfiguration object</returns>
        public static Mock<IConfiguration> CreateComprehensiveMockConfiguration(
            List<StackerInfo> stackerInfos,
            FanucRobotSettings fanucSettings,
            string serialPortName = "COM1",
            int serialBaudRate = 9600)
        {
            var mockConfiguration = new Mock<IConfiguration>();

            // Setup Stackers section
            var mockStackersSection = new Mock<IConfigurationSection>();
            mockConfiguration.Setup(x => x.GetSection("Stackers"))
                           .Returns(mockStackersSection.Object);
            
            mockStackersSection.Setup(x => x.Bind(It.IsAny<List<StackerInfo>>()))
                              .Callback<List<StackerInfo>>(list =>
                              {
                                  list.Clear();
                                  list.AddRange(stackerInfos);
                              });

            // Setup FanucRobot section
            var mockFanucSection = new Mock<IConfigurationSection>();
            mockConfiguration.Setup(x => x.GetSection("FanucRobot"))
                           .Returns(mockFanucSection.Object);

            // Setup SerialPort section
            mockConfiguration.Setup(x => x["SerialPort:Name"]).Returns(serialPortName);
            mockConfiguration.Setup(x => x["SerialPort:BaudRate"]).Returns(serialBaudRate.ToString());

            return mockConfiguration;
        }

        /// <summary>
        /// Creates a mock IConfigurationSection for testing section-specific scenarios.
        /// </summary>
        /// <param name="sectionName">Name of the section</param>
        /// <param name="keyValuePairs">Key-value pairs for the section</param>
        /// <returns>Mock IConfigurationSection object</returns>
        public static Mock<IConfigurationSection> CreateMockConfigurationSection(
            string sectionName, 
            Dictionary<string, string> keyValuePairs)
        {
            var mockSection = new Mock<IConfigurationSection>();
            
            mockSection.Setup(x => x.Key).Returns(sectionName);
            
            foreach (var kvp in keyValuePairs)
            {
                mockSection.Setup(x => x[kvp.Key]).Returns(kvp.Value);
            }

            return mockSection;
        }

        /// <summary>
        /// Creates test configuration values for common scenarios.
        /// </summary>
        public static class TestValues
        {
            public static readonly Dictionary<string, string> DefaultStackerConfig = new()
            {
                ["Name"] = "TestStacker",
                ["Port"] = "COM1",
                ["BaudRate"] = "9600"
            };

            public static readonly Dictionary<string, string> DefaultFanucConfig = new()
            {
                ["IpAddress"] = "*************",
                ["Enabled"] = "true",
                ["NumericRegister:ClassId"] = "0x6B",
                ["NumericRegister:InstanceId"] = "1",
                ["NumericRegister:Attribute"] = "31",
                ["StringRegister:ClassId"] = "0x6D",
                ["StringRegister:InstanceId"] = "1",
                ["StringRegister:Attribute"] = "11"
            };

            public static readonly Dictionary<string, string> DefaultSerialPortConfig = new()
            {
                ["Name"] = "COM1",
                ["BaudRate"] = "9600"
            };
        }
    }
}

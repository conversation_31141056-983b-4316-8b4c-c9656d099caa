using UBAStackerController.Models;
using System.IO.Ports;

namespace UBAStackerController.Tests.Helpers
{
    /// <summary>
    /// Builder class for creating test data objects with fluent interface.
    /// Provides convenient methods for setting up test scenarios with realistic data.
    /// </summary>
    public static class TestDataBuilder
    {
        #region Stacker Test Data

        /// <summary>
        /// Creates a StackerInfo builder for test scenarios.
        /// </summary>
        /// <returns>StackerInfoBuilder instance</returns>
        public static StackerInfoBuilder CreateStackerInfo()
        {
            return new StackerInfoBuilder();
        }

        /// <summary>
        /// Creates a StackerState builder for test scenarios.
        /// </summary>
        /// <returns>StackerStateBuilder instance</returns>
        public static StackerStateBuilder CreateStackerState()
        {
            return new StackerStateBuilder();
        }

        /// <summary>
        /// Creates a FanucRobotSettings builder for test scenarios.
        /// </summary>
        /// <returns>FanucRobotSettingsBuilder instance</returns>
        public static FanucRobotSettingsBuilder CreateFanucRobotSettings()
        {
            return new FanucRobotSettingsBuilder();
        }

        #endregion

        #region Default Test Data

        /// <summary>
        /// Creates a list of default stacker info objects for testing.
        /// </summary>
        /// <returns>List of StackerInfo objects</returns>
        public static List<StackerInfo> GetDefaultStackerInfos()
        {
            return new List<StackerInfo>
            {
                CreateStackerInfo()
                    .WithName("TestStacker1")
                    .WithPort("COM1")
                    .WithBaudRate(9600)
                    .Build(),
                CreateStackerInfo()
                    .WithName("TestStacker2")
                    .WithPort("COM2")
                    .WithBaudRate(9600)
                    .Build(),
                CreateStackerInfo()
                    .WithName("TestStacker3")
                    .WithPort("COM3")
                    .WithBaudRate(19200)
                    .Build()
            };
        }

        /// <summary>
        /// Creates default Fanuc robot settings for testing.
        /// </summary>
        /// <returns>FanucRobotSettings object</returns>
        public static FanucRobotSettings GetDefaultFanucSettings()
        {
            return CreateFanucRobotSettings()
                .WithIpAddress("*************")
                .WithEnabled(true)
                .WithNumericRegister("0x6B", 1, 31)
                .WithStringRegister("0x6D", 1, 11)
                .Build();
        }

        #endregion
    }

    #region Builder Classes

    /// <summary>
    /// Builder for StackerInfo objects.
    /// </summary>
    public class StackerInfoBuilder
    {
        private string _name = "DefaultStacker";
        private string _port = "COM1";
        private int _baudRate = 9600;

        public StackerInfoBuilder WithName(string name)
        {
            _name = name;
            return this;
        }

        public StackerInfoBuilder WithPort(string port)
        {
            _port = port;
            return this;
        }

        public StackerInfoBuilder WithBaudRate(int baudRate)
        {
            _baudRate = baudRate;
            return this;
        }

        public StackerInfo Build()
        {
            return new StackerInfo
            {
                Name = _name,
                Port = _port,
                BaudRate = _baudRate
            };
        }
    }

    /// <summary>
    /// Builder for StackerState objects.
    /// </summary>
    public class StackerStateBuilder
    {
        private string _name = "DefaultStacker";
        private StackerStatus _status = StackerStatus.Idling;
        private int _billInEscrow = 0;
        private bool _isActive = true;
        private string _lastUpdated = DateTime.Now.ToShortTimeString();
        private string _port = "COM1";

        public StackerStateBuilder WithName(string name)
        {
            _name = name;
            return this;
        }

        public StackerStateBuilder WithStatus(StackerStatus status)
        {
            _status = status;
            return this;
        }

        public StackerStateBuilder WithBillInEscrow(int billCount)
        {
            _billInEscrow = billCount;
            return this;
        }

        public StackerStateBuilder WithIsActive(bool isActive)
        {
            _isActive = isActive;
            return this;
        }

        public StackerStateBuilder WithLastUpdated(string lastUpdated)
        {
            _lastUpdated = lastUpdated;
            return this;
        }

        public StackerStateBuilder WithPort(string port)
        {
            _port = port;
            return this;
        }

        public StackerState Build()
        {
            return new StackerState
            {
                Name = _name,
                Status = _status,
                BillInEscrow = _billInEscrow,
                IsActive = _isActive,
                LastUpdated = _lastUpdated,
                Port = _port
            };
        }
    }

    /// <summary>
    /// Builder for FanucRobotSettings objects.
    /// </summary>
    public class FanucRobotSettingsBuilder
    {
        private string _ipAddress = "*************";
        private bool _enabled = true;
        private NumericRegisterSettings _numericRegister = new NumericRegisterSettings
        {
            ClassId = "0x6B",
            InstanceId = 1,
            Attribute = 31,
            Service = "SetAttributeSingle"
        };
        private StringRegisterSettings _stringRegister = new StringRegisterSettings
        {
            ClassId = "0x6D",
            InstanceId = 1,
            Attribute = 11,
            Service = "SetAttributeSingle"
        };

        public FanucRobotSettingsBuilder WithIpAddress(string ipAddress)
        {
            _ipAddress = ipAddress;
            return this;
        }

        public FanucRobotSettingsBuilder WithEnabled(bool enabled)
        {
            _enabled = enabled;
            return this;
        }

        public FanucRobotSettingsBuilder WithNumericRegister(string classId, int instanceId, int attribute)
        {
            _numericRegister = new NumericRegisterSettings
            {
                ClassId = classId,
                InstanceId = instanceId,
                Attribute = attribute,
                Service = "SetAttributeSingle"
            };
            return this;
        }

        public FanucRobotSettingsBuilder WithStringRegister(string classId, int instanceId, int attribute)
        {
            _stringRegister = new StringRegisterSettings
            {
                ClassId = classId,
                InstanceId = instanceId,
                Attribute = attribute,
                Service = "SetAttributeSingle"
            };
            return this;
        }

        public FanucRobotSettings Build()
        {
            return new FanucRobotSettings
            {
                IpAddress = _ipAddress,
                Enabled = _enabled,
                NumericRegister = _numericRegister,
                StringRegister = _stringRegister
            };
        }
    }

    #endregion
}

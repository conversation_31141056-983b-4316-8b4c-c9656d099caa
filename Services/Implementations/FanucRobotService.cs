using Sres.Net.EEIP;
using Microsoft.Extensions.Options;
using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Services.Implementations
{
    public class FanucRobotService : IFanucRobotService
    {
        private readonly EEIPClient _eEIPClient;
        private readonly FanucRobotSettings _settings;
        private readonly ILogger<FanucRobotService> _logger;
        private readonly object _lock = new();
        private bool _robotIsConnected;
        private byte[]? _lastNumericWrittenValue;
        private byte[]? _lastStringWrittenValue;


        public FanucRobotService(EEIPClient eEIPClient, IOptions<FanucRobotSettings> settings, ILogger<FanucRobotService> logger)
        {
            _settings = settings.Value;
            _eEIPClient = eEIPClient;
            _logger = logger;
        }

        public async Task ReadRegisterAsync(int register)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; ReadRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.NumericRegister.ClassId, 16);
                        var response = _eEIPClient.GetAttributeSingle(
                            classId,
                            _settings.NumericRegister.InstanceId,
                            _settings.NumericRegister.Attribute);
                        _logger.LogInformation("Read register {Attribute}: {Response}", _settings.NumericRegister.Attribute, BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error reading Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        public async Task WriteToNumericRegisterAsync(int register, byte[] value)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; WriteToRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        if (_lastNumericWrittenValue != null && value.SequenceEqual(_lastNumericWrittenValue))
                        {
                            _logger.LogTrace("Skipping write to Fanuc register because value has not changed.");
                            return;
                        }

                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.NumericRegister.ClassId, 16);
                        _logger.LogDebug("Writing to register {Attribute} value {Value}", _settings.NumericRegister.Attribute, BitConverter.ToString(value));

                        var response = _eEIPClient.SetAttributeSingle(
                            classId,
                            _settings.NumericRegister.InstanceId,
                            register,
                            value);

                        _lastNumericWrittenValue = value;
                        _logger.LogInformation("Write response: {Response}", BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error writing Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        public async Task WriteToStringRegisterAsync(int register, string value)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; WriteToRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        // Convert string to bytes using ASCII encoding 
                        var stringBytes = System.Text.Encoding.ASCII.GetBytes(value);

                        // Ensure string doesn't exceed 82 bytes maximum
                        if (stringBytes.Length > 82)
                        {
                            throw new ArgumentException($"String value exceeds maximum length of 82 bytes. Current length: {stringBytes.Length}");
                        }

                        /*
                        FANUC String Format (88 bytes total):
                        0–3 Bytes:     String Length (UINT32, little-endian)
                        4–85 Bytes:    String data (82 bytes max, padded with zeros)
                        86–87 Bytes:   Padding (2 bytes, always zero)
                        */

                        // Create the 88-byte FANUC string structure
                        var fanucStringData = new byte[88];

                        // Bytes 0-3: String length as UINT32 (little-endian)
                        var lengthBytes = BitConverter.GetBytes((uint)stringBytes.Length);
                        Array.Copy(lengthBytes, 0, fanucStringData, 0, 4);

                        // Bytes 4-85: String data (82 bytes, zero-padded if shorter)
                        Array.Copy(stringBytes, 0, fanucStringData, 4, stringBytes.Length);
                        // Remaining bytes in the 82-byte string area are already zero-initialized

                        // Bytes 86-87: Padding (already zero-initialized)

                        // Check if this is the same as the last written value
                        if (_lastStringWrittenValue != null && fanucStringData.SequenceEqual(_lastStringWrittenValue))
                        {
                            _logger.LogTrace("Skipping write to Fanuc string register because value has not changed.");
                            return;
                        }

                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.StringRegister.ClassId, 16);
                        _logger.LogDebug("Writing to string register {Attribute} value '{Value}' (length: {Length})",
                            _settings.StringRegister.Attribute, value, stringBytes.Length);

                        var response = _eEIPClient.SetAttributeSingle(
                            classId,
                            _settings.StringRegister.InstanceId,
                            register,
                            fanucStringData);

                        _lastStringWrittenValue = fanucStringData;
                        _logger.LogInformation("Write response: {Response}", BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error writing Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        private void EnsureConnected()
        {
            if (!_robotIsConnected)
            {
                _eEIPClient.RegisterSession(_settings.IpAddress);
                _robotIsConnected = true;
                _logger.LogInformation("Registered Fanuc EEIP session to {Ip}", _settings.IpAddress);
            }
        }

        private void DisconnectIfConnected()
        {
            if (_robotIsConnected)
            {
                _eEIPClient.UnRegisterSession();
                _robotIsConnected = false;
                _logger.LogInformation("Unregistered Fanuc EEIP session.");
            }
        }
    }
}